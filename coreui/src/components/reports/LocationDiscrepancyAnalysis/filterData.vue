<template>
  <c-card>
    <c-card-header>Location Discrepancy Analysis Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-location-pin" /> Main Filters
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input
                    label="From Date"
                    type="date"
                    placeholder="From Date"
                    v-model="from_date"
                  ></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input
                    label="To Date"
                    type="date"
                    placeholder="To Date"
                    v-model="to_date"
                    @input="getAllData"
                  ></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Distance Threshold (meters)</strong>
                    </label>
                      <c-input
                        type="number"
                        v-model.number="distance_threshold"
                        placeholder="1000"
                        min="0"
                        step="100"
                      />
                  </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Recent Visits Limit</strong>
                    </label>
                      <c-input
                        type="number"
                        v-model.number="recent_visits_limit"
                        placeholder="5"
                        min="1"
                        max="50"
                      />
                  </div>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Selection
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Line</strong>
                    </label>
                    <select-all-checkbox
                      :items="lines"
                      v-model="checkAllLines"
                    />

                    <v-select
                      v-model="line_id"
                      :options="lines"
                      label="name"
                      :value="0"
                      :reduce="(line) => line.id"
                      placeholder="Select Lines"
                      multiple
                      @input="getLineData"
                    />
                  </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Accounts</strong>
                    </label>
                    <select-all-checkbox
                      :items="types"
                      v-model="checkAllAccounts"
                    />

                    <v-select
                      v-model="account_id"
                      :options="accounts"
                      label="name"
                      :reduce="(account) => account.id"
                      placeholder="Select Accounts"
                      multiple
                      :filterable="true"
                    />
                  </div>
                </div>

                <div class="col-lg-4 col-md-4 col-sm-8">
                  <div class="form-group">
                    <label>
                      <strong>Account Type</strong>
                    </label>
                    <select-all-checkbox
                      :items="types"
                      v-model="allAccountTypes"
                    />

                    <v-select
                      v-model="type_id"
                      :options="types"
                      label="name"
                      :reduce="(type) => type.id"
                      placeholder="Select Account Type"
                      multiple
                    />
                  </div>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button
        color="primary"
        class="text-white"
        @click="show"
        style="float: right"
        :disabled="isLoading"
      >
        <c-spinner v-if="isLoading" size="sm" class="mr-2" />
        {{ isLoading ? 'Loading...' : 'Generate Report' }}
      </c-button>
    </c-card-footer>
  </c-card>
</template>

<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import SelectAllCheckbox from "../../common/SelectAllCheckBox.vue";

export default {
  components: {
    SelectAllCheckbox,
    vSelect,
  },
  emits: ["getLocationDiscrepancy"],
  data() {
    return {
      // Filter values
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
      distance_threshold: 1000,
      recent_visits_limit: 5,

      // Account selection
      account_id: [],
      line_id: [],
      type_id: [],

      // Data arrays
      accounts: [368596],
      lines: [],
      types: [],

      // Checkbox states
      checkAllAccounts: false,
      checkAllLines: false,
      allAccountTypes: true,

      // Loading state
      isLoading: false,
    };
  },
  methods: {
    initialize() {
      this.isLoading = true;
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'accountTypes']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);
        })
        .catch((error) => {
          this.showErrorMessage(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    getAllData() {
      this.line_id = [];
      this.account_id = [];
      this.type_id = [];
      this.accounts = [];
      this.initialize();
    },

    getLineData() {
      if (this.line_id.length === 0) {
        this.accounts = [];
        return;
      }

      this.isLoading = true;
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['accounts']
        })
        .then((response) => {
          this.accounts = response.data.data.accounts || [];
        })
        .catch((error) => {
          this.showErrorMessage(error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    checkAllLine() {
      if (this.checkAllLines) {
        this.line_id = this.lines.map((item) => item.id);
      } else {
        this.line_id = [];
      }
      this.getLineData();
    },

    checkAllAccountsHandler() {
      if (this.checkAllAccounts) {
        this.account_id = this.accounts.map((item) => item.id);
      } else {
        this.account_id = [];
      }
    },

    checkAllAccountTypes() {
      if (this.allAccountTypes) {
        this.type_id = this.types.map((item) => item.id);
      } else {
        this.type_id = [];
      }
    },

    show() {
      const filters = {
        from_date: this.from_date,
        to_date: this.to_date,
        distance_threshold: this.distance_threshold,
        recent_visits_limit: this.recent_visits_limit,
        account_ids: this.account_id,
        line_ids: this.line_id,
        type_ids: this.type_id,
      };

      this.$emit("getLocationDiscrepancy", { filters });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
