<template>
  <div class="table-body-container" :class="containerClasses">
    <!-- Virtual Scrolling Implementation -->
    <div v-if="virtualScrolling" class="virtual-scroll-container">
      <!-- Top Spacer -->
      <div class="virtual-spacer-top" :style="{ height: topSpacerHeight + 'px' }"></div>
      
      <!-- Visible Rows -->
      <table class="body-table">
        <tbody class="table-body">
          <TableRow
            v-for="(item, index) in items"
            :key="getItemKey(item, index)"
            :item="item"
            :index="startIndex + index"
            :columns="columns"
            :selected="isItemSelected(item)"
            :selectable="selectable"
            :select-on-row-click="selectOnRowClick"
            :multiple-selection="multipleSelection"
            :row-class="getRowClass(item, startIndex + index)"
            @click="handleRowClick"
            @select="handleRowSelect"
            @hover="handleRowHover"
          >
            <!-- Pass through all scoped slots -->
            <template v-for="(_, slot) of $scopedSlots" v-slot:[slot]="scope">
              <slot :name="slot" v-bind="scope" />
            </template>
          </TableRow>
        </tbody>
      </table>
      
      <!-- Bottom Spacer -->
      <div class="virtual-spacer-bottom" :style="{ height: bottomSpacerHeight + 'px' }"></div>
    </div>

    <!-- Traditional Scrolling Implementation -->
    <div v-else class="traditional-scroll-container">
      <table class="body-table">
        <tbody class="table-body">
          <TableRow
            v-for="(item, index) in items"
            :key="getItemKey(item, index)"
            :item="item"
            :index="index"
            :columns="columns"
            :selected="isItemSelected(item)"
            :selectable="selectable"
            :select-on-row-click="selectOnRowClick"
            :multiple-selection="multipleSelection"
            :row-class="getRowClass(item, index)"
            @click="handleRowClick"
            @select="handleRowSelect"
            @hover="handleRowHover"
          >
            <!-- Pass through all scoped slots -->
            <template v-for="(_, slot) of $scopedSlots" v-slot:[slot]="scope">
              <slot :name="slot" v-bind="scope" />
            </template>
          </TableRow>
        </tbody>
      </table>
    </div>

    <!-- Loading Overlay -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner-large"></div>
        <span class="loading-text">Loading data...</span>
      </div>
    </div>
  </div>
</template>

<script>
import TableRow from './TableRow.vue'

export default {
  name: 'TableBody',
  
  components: {
    TableRow
  },

  props: {
    items: {
      type: Array,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selectOnRowClick: {
      type: Boolean,
      default: true
    },
    multipleSelection: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    virtualScrolling: {
      type: Boolean,
      default: false
    },
    rowHeight: {
      type: Number,
      default: 40
    },
    startIndex: {
      type: Number,
      default: 0
    },
    endIndex: {
      type: Number,
      default: 0
    },
    topSpacerHeight: {
      type: Number,
      default: 0
    },
    bottomSpacerHeight: {
      type: Number,
      default: 0
    },
    itemKey: {
      type: [String, Function],
      default: null
    },
    rowClassFunction: {
      type: Function,
      default: null
    },
    striped: {
      type: Boolean,
      default: true
    },
    hover: {
      type: Boolean,
      default: true
    }
  },

  computed: {
    /**
     * Container CSS classes
     */
    containerClasses() {
      return [
        'table-body-vue2',
        {
          'table-body--virtual': this.virtualScrolling,
          'table-body--loading': this.loading,
          'table-body--striped': this.striped,
          'table-body--hover': this.hover,
          'table-body--selectable': this.selectable
        }
      ]
    }
  },

  methods: {
    /**
     * Get unique key for item
     */
    getItemKey(item, index) {
      if (this.itemKey) {
        if (typeof this.itemKey === 'function') {
          return this.itemKey(item, index)
        } else if (typeof this.itemKey === 'string') {
          return item[this.itemKey] || index
        }
      }
      
      // Try common ID fields
      if (item.id !== undefined) return item.id
      if (item._id !== undefined) return item._id
      if (item.uuid !== undefined) return item.uuid
      
      // Fallback to index
      return index
    },

    /**
     * Check if item is selected
     */
    isItemSelected(item) {
      if (!this.selectable || !this.selectedItems.length) {
        return false
      }
      
      return this.selectedItems.some(selectedItem => {
        return this.compareItems(item, selectedItem)
      })
    },

    /**
     * Compare two items for equality
     */
    compareItems(item1, item2) {
      // Use item key if available
      if (this.itemKey) {
        const key1 = this.getItemKey(item1, 0)
        const key2 = this.getItemKey(item2, 0)
        return key1 === key2
      }

      // Try to use ID fields for comparison
      if (item1.id !== undefined && item2.id !== undefined) {
        return item1.id === item2.id
      }
      if (item1._id !== undefined && item2._id !== undefined) {
        return item1._id === item2._id
      }
      if (item1.uuid !== undefined && item2.uuid !== undefined) {
        return item1.uuid === item2.uuid
      }

      // Fallback to deep comparison for simple objects
      return JSON.stringify(item1) === JSON.stringify(item2)
    },

    /**
     * Get row CSS class
     */
    getRowClass(item, index) {
      let classes = []
      
      // Default row classes
      classes.push('data-row')
      
      // Striped rows
      if (this.striped && index % 2 === 1) {
        classes.push('data-row--striped')
      }
      
      // Selected row
      if (this.isItemSelected(item)) {
        classes.push('data-row--selected')
      }
      
      // Custom row class function
      if (this.rowClassFunction) {
        const customClass = this.rowClassFunction(item, index)
        if (customClass) {
          if (Array.isArray(customClass)) {
            classes = classes.concat(customClass)
          } else {
            classes.push(customClass)
          }
        }
      }
      
      return classes
    },

    /**
     * Handle row click
     */
    handleRowClick(payload) {
      this.$emit('row-click', payload)
    },

    /**
     * Handle row selection
     */
    handleRowSelect(payload) {
      this.$emit('row-select', payload)
    },

    /**
     * Handle row hover
     */
    handleRowHover(payload) {
      this.$emit('row-hover', payload)
    }
  }
}
</script>

<style lang="scss" scoped>
/* Table Body Styles - Matching GenericDataTable Design */

.table-body-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  background: white;
}

.table-body-vue2 {
  &--virtual {
    .virtual-scroll-container {
      position: relative;
      width: 100%;
    }
  }
  
  &--loading {
    pointer-events: none;
    
    .body-table {
      opacity: 0.5;
    }
  }
  
  &--striped {
    .data-row:nth-child(even) {
      background-color: #f8fafc;
    }
  }
  
  &--hover {
    .data-row:hover {
      background-color: #edf2f7;
      transform: translateZ(0);
    }
  }
}

.body-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto; /* Changed from fixed to auto for horizontal scrolling */
  background: white;
  min-width: max-content; /* Ensure table expands to fit content */
}

.table-body {
  background: white;
}

.virtual-spacer-top,
.virtual-spacer-bottom {
  width: 100%;
  pointer-events: none;
}

.traditional-scroll-container {
  width: 100%;
  height: 100%;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(2px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.loading-spinner-large {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

/* Row Styles */
.data-row {
  transition: background-color 0.2s ease, transform 0.1s ease;
  will-change: background-color;
  contain: layout style;
  
  &--striped {
    background-color: #f8fafc;
  }
  
  &--selected {
    background: linear-gradient(135deg, #e6fffa 0%, #d1fae5 100%) !important;
    border-left: 4px solid #10b981;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
  }
  
  &:hover {
    background-color: #edf2f7;
    transform: translateZ(0);
  }
}

/* Dark Theme */
.vue2-datatable--dark {
  .table-body-container,
  .body-table,
  .table-body {
    background: #1a202c;
    color: white;
  }
  
  .data-row {
    &--striped {
      background-color: #2d3748;
    }
    
    &--selected {
      background: linear-gradient(135deg, #065f46 0%, #047857 100%) !important;
      border-left-color: #10b981;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
    }
    
    &:hover {
      background-color: #2d3748;
    }
  }
  
  .loading-overlay {
    background: rgba(26, 32, 44, 0.8);
  }
  
  .loading-content {
    background: #2d3748;
    color: white;
  }
  
  .loading-text {
    color: #a0aec0;
  }
}

/* Compact Theme */
.vue2-datatable--compact {
  .data-row {
    height: 32px;
  }
}

/* Performance Optimizations */
.virtual-scroll-container,
.traditional-scroll-container {
  contain: layout style paint;
}

.data-row {
  contain: layout style;
}

/* Responsive Design */
@media (max-width: 768px) {
  .loading-content {
    padding: 20px;
  }
  
  .loading-spinner-large {
    width: 28px;
    height: 28px;
    border-width: 2px;
  }
  
  .loading-text {
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .loading-content {
    padding: 16px;
    gap: 12px;
  }
  
  .loading-spinner-large {
    width: 24px;
    height: 24px;
  }
  
  .loading-text {
    font-size: 12px;
  }
}

/* Print Styles */
@media print {
  .loading-overlay {
    display: none;
  }
  
  .table-body-container {
    overflow: visible;
    height: auto;
  }
  
  .data-row:hover {
    background-color: transparent !important;
  }
}
</style>
