<template>
  <div class="table-header-container">
    <table class="header-table">
      <thead class="table-header-sticky">
        <tr class="header-row">
          <th
            v-for="column in columns"
            :key="column.key"
            :class="getHeaderClass(column)"
            :style="getHeaderStyle(column)"
            @click="handleHeaderClick(column)"
            @mousedown="startResize($event, column)"
          >
            <!-- Selection Column -->
            <div v-if="column.key === 'select'" class="select-all-container">
              <input
                type="checkbox"
                class="select-all-checkbox"
                :checked="selectAll"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
                title="Select All"
              />
              <span class="select-all-label">All</span>
            </div>

            <!-- Regular Column -->
            <div v-else class="header-content">
              <span class="header-label">{{ column.label }}</span>
              
              <!-- Sort Indicator -->
              <div v-if="column.sortable" class="sort-indicator">
                <CIcon
                  v-if="getSortDirection(column.key)"
                  :name="getSortIcon(column.key)"
                  :class="getSortIconClass(column.key)"
                />
                <CIcon
                  v-else
                  name="cil-sort"
                  class="sort-icon-inactive"
                />
                
                <!-- Multi-sort priority -->
                <span
                  v-if="getSortPriority(column.key) && getSortPriority(column.key) > 1"
                  class="sort-priority"
                >
                  {{ getSortPriority(column.key) }}
                </span>
              </div>
            </div>

            <!-- Column Resizer -->
            <div
              v-if="column.resizable !== false"
              class="column-resizer"
              @mousedown.stop="startResize($event, column)"
            ></div>
          </th>
        </tr>
      </thead>
    </table>
  </div>
</template>

<script>
export default {
  name: 'TableHeader',
  
  props: {
    columns: {
      type: Array,
      required: true
    },
    sortColumn: {
      type: String,
      default: null
    },
    sortDirection: {
      type: String,
      default: 'asc'
    },
    sortIndicators: {
      type: Object,
      default: () => ({})
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selectAll: {
      type: Boolean,
      default: false
    },
    isIndeterminate: {
      type: Boolean,
      default: false
    },
    selectedCount: {
      type: Number,
      default: 0
    },
    totalCount: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      resizing: false,
      resizeColumn: null,
      resizeStartX: 0,
      resizeStartWidth: 0
    }
  },

  computed: {
    // Computed properties removed - using props instead
  },

  mounted() {
    // Add global mouse event listeners for column resizing
    document.addEventListener('mousemove', this.handleResize)
    document.addEventListener('mouseup', this.stopResize)
  },

  beforeDestroy() {
    // Remove global event listeners
    document.removeEventListener('mousemove', this.handleResize)
    document.removeEventListener('mouseup', this.stopResize)
  },

  methods: {
    /**
     * Get header cell CSS classes
     */
    getHeaderClass(column) {
      return [
        'header-cell',
        `header-cell--${column.key}`,
        {
          'header-cell--sortable': column.sortable,
          'header-cell--sorted': this.getSortDirection(column.key),
          'header-cell--select': column.key === 'select',
          'header-cell--fixed-left': column.fixed === 'left',
          'header-cell--fixed-right': column.fixed === 'right',
          'header-cell--resizable': column.resizable !== false
        }
      ]
    },

    /**
     * Get header cell inline styles
     */
    getHeaderStyle(column) {
      const styles = {}
      
      if (column.width) {
        if (typeof column.width === 'number') {
          styles.width = `${column.width}px`
          styles.minWidth = `${column.width}px`
        } else {
          styles.width = column.width
          styles.minWidth = column.width
        }
      }
      
      if (column.align) {
        styles.textAlign = column.align
      }
      
      return styles
    },

    /**
     * Handle header click for sorting
     */
    handleHeaderClick(column) {
      if (column.sortable && column.key !== 'select') {
        this.$emit('sort', column.key)
      }
    },

    /**
     * Handle select all checkbox
     */
    handleSelectAll(event) {
      this.$emit('select-all', event.target.checked)
    },

    /**
     * Get sort direction for column
     */
    getSortDirection(columnKey) {
      const indicator = this.sortIndicators[columnKey]
      return indicator ? indicator.direction : null
    },

    /**
     * Get sort priority for column
     */
    getSortPriority(columnKey) {
      const indicator = this.sortIndicators[columnKey]
      return indicator ? indicator.priority : null
    },

    /**
     * Get sort icon name
     */
    getSortIcon(columnKey) {
      const direction = this.getSortDirection(columnKey)
      return direction === 'asc' ? 'cil-sort-ascending' : 'cil-sort-descending'
    },

    /**
     * Get sort icon CSS class
     */
    getSortIconClass(columnKey) {
      const direction = this.getSortDirection(columnKey)
      return [
        'sort-icon',
        `sort-icon--${direction}`,
        {
          'sort-icon--active': !!direction
        }
      ]
    },

    /**
     * Start column resize
     */
    startResize(event, column) {
      if (column.resizable === false) return
      
      event.preventDefault()
      this.resizing = true
      this.resizeColumn = column
      this.resizeStartX = event.clientX
      
      // Get current column width
      const headerCell = event.target.closest('.header-cell')
      if (headerCell) {
        this.resizeStartWidth = headerCell.offsetWidth
      }
      
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    },

    /**
     * Handle column resize
     */
    handleResize(event) {
      if (!this.resizing || !this.resizeColumn) return
      
      event.preventDefault()
      const deltaX = event.clientX - this.resizeStartX
      const newWidth = Math.max(50, this.resizeStartWidth + deltaX) // Minimum width of 50px
      
      this.$emit('column-resize', this.resizeColumn, newWidth)
    },

    /**
     * Stop column resize
     */
    stopResize() {
      if (this.resizing) {
        this.resizing = false
        this.resizeColumn = null
        this.resizeStartX = 0
        this.resizeStartWidth = 0
        
        document.body.style.cursor = ''
        document.body.style.userSelect = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* Table Header Styles - Enhanced Professional Design */

.table-header-container {
  position: sticky;
  top: 0;
  z-index: 200;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  border-bottom: 3px solid #1a202c;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  /* Remove overflow hidden to allow horizontal scrolling */
  overflow: visible;
}

.header-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto; /* Changed from fixed to auto for horizontal scrolling */
  background: transparent;
  min-width: max-content; /* Ensure table expands to fit content */
}

.table-header-sticky {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  position: sticky;
  top: 0;
  z-index: 200;
  border-bottom: none;
}

.header-row {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  border-bottom: none;
  height: 56px;
}

.header-cell {
  position: relative;
  padding: 16px 20px;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  border-right: 1px solid rgba(255, 255, 255, 0.15);
  font-weight: 700;
  font-size: 14px;
  color: white;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  /* Remove overflow hidden to allow proper width calculation */
  overflow: visible;
  /* Remove text-overflow ellipsis to allow full content width */
  user-select: none;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  /* Ensure minimum width for horizontal scrolling */
  min-width: 120px;

  &:last-child {
    border-right: none;
  }

  &--sortable {
    cursor: pointer;

    &:hover {
      background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);

      .sort-icon-inactive {
        color: rgba(255, 255, 255, 0.9);
        transform: scale(1.1);
      }
    }
  }

  &--sorted {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    color: #fbbf24;
    box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.2);
    border-bottom: 3px solid #fbbf24;
  }

  &--select {
    width: 70px;
    min-width: 70px;
    max-width: 70px;
    text-align: center;
    padding: 16px 12px;
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  }

  &--fixed-left {
    position: sticky;
    left: 0;
    z-index: 201;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }

  &--fixed-right {
    position: sticky;
    right: 0;
    z-index: 201;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  }

  &--resizable {
    &:hover .column-resizer {
      opacity: 1;
    }
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.header-label {
  flex: 1;
  font-weight: 700;
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
  text-align: center;
}

.sort-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.sort-icon {
  width: 18px;
  height: 18px;
  transition: all 0.3s ease;

  &--active {
    color: #fbbf24;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transform: scale(1.1);
  }

  &--asc {
    color: #10b981;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transform: scale(1.1);
  }

  &--desc {
    color: #f59e0b;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transform: scale(1.1);
  }
}

.sort-icon-inactive {
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.sort-priority {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  color: white;
  font-size: 10px;
  font-weight: 700;
  padding: 3px 6px;
  border-radius: 6px;
  min-width: 18px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.select-all-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 4px;
}

.select-all-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: #10b981;
  transform: scale(1.2);
  border-radius: 4px;
  border: 2px solid white;
  background: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;

  &:checked {
    accent-color: #10b981;
    background-color: #10b981;
    border-color: #10b981;
    box-shadow: 0 3px 8px rgba(16, 185, 129, 0.4);
  }

  &:indeterminate {
    accent-color: #f59e0b;
    background-color: #f59e0b;
    border-color: #f59e0b;
    box-shadow: 0 3px 8px rgba(245, 158, 11, 0.4);
  }

  &:hover {
    transform: scale(1.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

.select-all-label {
  font-weight: 700;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  cursor: col-resize;
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 0 4px 4px 0;

  &:hover {
    opacity: 1;
    width: 8px;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
  }
}

/* Enhanced Hover Effects */
.header-cell--sortable:hover .sort-indicator {
  opacity: 1;
  transform: scale(1.05);
}

.header-cell--sortable:hover .header-label {
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-cell {
    padding: 14px 16px;
    font-size: 13px;
    height: 52px;
  }

  .header-label {
    font-size: 13px;
  }

  .sort-icon,
  .sort-icon-inactive {
    width: 16px;
    height: 16px;
  }

  .select-all-checkbox {
    width: 18px;
    height: 18px;
    transform: scale(1.1);
  }

  .select-all-label {
    font-size: 11px;
  }
}

@media (max-width: 576px) {
  .header-cell {
    padding: 12px 14px;
    font-size: 12px;
    height: 48px;
  }

  .header-content {
    gap: 6px;
  }

  .sort-priority {
    font-size: 9px;
    padding: 2px 4px;
  }

  .select-all-checkbox {
    width: 16px;
    height: 16px;
    transform: scale(1.0);
  }
}

/* Dark Theme */
.vue2-datatable--dark {
  .table-header-container,
  .header-table,
  .table-header-sticky,
  .header-row,
  .header-cell {
    background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
    color: white;
    border-color: #374151;
  }

  .header-cell {
    &--sortable:hover {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
    }

    &--sorted {
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      color: #fbbf24;
      border-bottom-color: #fbbf24;
    }

    &--select {
      background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    }
  }

  .header-label {
    color: white;
  }

  .select-all-label {
    color: white;
  }
}

/* Animation Enhancements */
@keyframes headerGlow {
  0% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
  50% { box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25); }
  100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
}

.table-header-container {
  animation: headerGlow 3s ease-in-out infinite;
}

/* Focus and Accessibility */
.select-all-checkbox:focus {
  outline: 3px solid rgba(16, 185, 129, 0.5);
  outline-offset: 2px;
}

.header-cell--sortable:focus {
  outline: 2px solid rgba(251, 191, 36, 0.6);
  outline-offset: -2px;
}

/* Print Styles */
@media print {
  .table-header-container {
    position: static;
    background: #f8fafc !important;
    color: #000 !important;
    box-shadow: none !important;
    animation: none !important;
  }

  .header-cell {
    background: #f8fafc !important;
    color: #000 !important;
    border: 1px solid #000 !important;
  }

  .column-resizer {
    display: none;
  }

  .sort-indicator {
    display: none;
  }

  .select-all-checkbox {
    display: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .header-cell {
    border: 2px solid white;
    background: #000 !important;
    color: white !important;
  }

  .select-all-checkbox {
    border: 3px solid white !important;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .header-cell,
  .sort-icon,
  .sort-icon-inactive,
  .select-all-checkbox,
  .column-resizer {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }

  .table-header-container {
    animation: none !important;
  }
}
</style>
