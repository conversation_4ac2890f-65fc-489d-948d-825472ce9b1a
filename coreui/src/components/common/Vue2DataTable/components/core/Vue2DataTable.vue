<template>
  <div class="vue2-datatable-container" :class="containerClasses">
    <!-- Performance monitoring display (development only) -->
    <div v-if="enablePerformanceMonitoring && isDevelopment" class="performance-monitor">
      <div class="performance-stats">
        <span>Render: {{ renderTime }}ms</span>
        <span>Memory: {{ memoryUsage }}MB</span>
        <span>Items: {{ totalFilteredItems }}</span>
      </div>
    </div>

    <!-- Search Bar -->
    <SearchBar
      v-if="showSearch"
      v-model="searchTerm"
      :placeholder="searchPlaceholder"
      :suggestions="searchSuggestions"
      :loading="isSearching"
      :stats="searchStats"
      @clear="clearSearch"
      @save-search="saveCurrentSearch"
      @load-search="loadSavedSearch"
    />

    <!-- Table Container with Enhanced Scroll Synchronization -->
    <div class="table-container-vue2" ref="tableContainer" @scroll="handleScroll" @wheel="handleWheel">
      <!-- Total Records Bar -->
      <div v-if="showTotalBar" class="total-records-bar">
        <div class="total-info">
          <div class="total-section">
            <CIcon name="cil-chart-pie" class="total-icon" />
            <span class="total-label">Total Records:</span>
            <span class="total-count-badge">{{ totalFilteredItems }}</span>
          </div>
          <div v-if="hasActiveSearch && totalFilteredItems !== items.length" class="filter-section">
            <CIcon name="cil-filter" class="filter-icon" />
            <span class="filter-text">Filtered from {{ items.length }} total</span>
          </div>
          <div class="page-section">
            <CIcon name="cil-layers" class="page-icon" />
            <span class="page-text">
              Page {{ currentPage }} of {{ totalPages }}
            </span>
          </div>
        </div>
      </div>

      <!-- Enhanced Table Header with Scroll Sync -->
      <div class="table-header-wrapper" ref="headerWrapper">
        <TableHeader
          ref="tableHeader"
          :columns="visibleColumns"
          :sort-column="sortColumn"
          :sort-direction="sortDirection"
          :sort-indicators="sortIndicators"
          :selectable="selectable"
          :select-all="isAllSelected"
          :is-indeterminate="isIndeterminate"
          :selected-count="currentSelectedItems.length"
          :total-count="totalFilteredItems"
          :scroll-left="scrollLeft"
          @sort="sortBy"
          @select-all="handleSelectAll"
          @column-resize="handleColumnResize"
        />
      </div>

      <!-- Enhanced Table Body with Scroll Sync -->
      <div class="table-body-wrapper" ref="bodyWrapper">
        <TableBody
          ref="tableBody"
          :items="visibleItems"
          :columns="visibleColumns"
          :selected-items="currentSelectedItems"
          :selectable="selectable"
          :select-on-row-click="selectOnRowClick"
          :multiple-selection="multipleSelection"
          :loading="loading"
          :virtual-scrolling="shouldUseVirtualScrolling"
          :row-height="rowHeight"
          :start-index="startIndex"
          :end-index="endIndex"
          :top-spacer-height="topSpacerHeight"
          :bottom-spacer-height="bottomSpacerHeight"
          :scroll-left="scrollLeft"
          @row-click="handleRowClick"
          @row-select="handleRowSelect"
          @row-hover="handleRowHover"
        >
          <!-- Pass through all scoped slots -->
          <template v-for="(_, slot) of $scopedSlots" v-slot:[slot]="scope">
            <slot :name="slot" v-bind="scope" />
          </template>
        </TableBody>
      </div>

      <!-- Loading Spinner -->
      <LoadingSpinner v-if="loading" :message="loadingMessage" />

      <!-- Empty State -->
      <EmptyState
        v-if="!loading && totalFilteredItems === 0"
        :has-search="hasActiveSearch"
        :search-term="searchTerm"
        @clear-search="clearSearch"
        @clear-filters="clearFilters"
      />
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="showPagination && !virtualScrollEnabled"
      :current-page="currentPage"
      :page-size="pageSize"
      :total-items="totalFilteredItems"
      :page-size-options="pageSizeOptions"
      :loading="loading"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />

    <!-- Error Display -->
    <div v-if="error" class="error-container">
      <div class="error-message">
        <CIcon name="cil-warning" class="error-icon" />
        <span>{{ error.message || 'An error occurred while loading data' }}</span>
        <CButton
          v-if="canRetry"
          color="primary"
          size="sm"
          @click="retry"
          class="retry-button"
        >
          Retry
        </CButton>
      </div>
    </div>
  </div>
</template>

<script>
import SearchBar from './SearchBar.vue'
import TableHeader from './TableHeader.vue'
import TableBody from './TableBody.vue'
import Pagination from './Pagination.vue'
import LoadingSpinner from './LoadingSpinner.vue'
import EmptyState from './EmptyState.vue'

// Mixins
import VirtualScrollMixin from '../../mixins/VirtualScroll.js'
import DataManagerMixin from '../../mixins/DataManager.js'
import SearchMixin from '../../mixins/SearchMixin.js'
import SortingMixin from '../../mixins/SortingMixin.js'

// Utilities
import { validateProps, validateColumns } from '../../utils/validators.js'
import { globalPerformanceMonitor } from '../../utils/performance.js'

export default {
  name: 'Vue2DataTable',
  
  components: {
    SearchBar,
    TableHeader,
    TableBody,
    Pagination,
    LoadingSpinner,
    EmptyState
  },

  mixins: [
    VirtualScrollMixin,
    DataManagerMixin,
    SearchMixin,
    SortingMixin
  ],

  props: {
    // Core data props
    columns: {
      type: Array,
      required: true,
      validator: validateColumns
    },
    dataSource: {
      type: [Array, String, Function],
      required: true
    },

    // Display options
    height: {
      type: [String, Number],
      default: 'auto'
    },
    striped: {
      type: Boolean,
      default: true
    },
    bordered: {
      type: Boolean,
      default: true
    },
    hover: {
      type: Boolean,
      default: true
    },
    theme: {
      type: String,
      default: 'default',
      validator: value => ['default', 'dark', 'compact', 'minimal'].includes(value)
    },

    // Feature toggles
    searchable: {
      type: Boolean,
      default: true
    },
    sortable: {
      type: Boolean,
      default: true
    },
    paginated: {
      type: Boolean,
      default: true
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selectOnRowClick: {
      type: Boolean,
      default: true
    },
    multipleSelection: {
      type: Boolean,
      default: true
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    showTotalBar: {
      type: Boolean,
      default: true
    },

    // Search props
    searchPlaceholder: {
      type: String,
      default: 'Search in all fields...'
    },
    searchTerm: {
      type: String,
      default: ''
    },

    // Selection props
    selectedItems: {
      type: Array,
      default: () => []
    },
    selectAll: {
      type: Boolean,
      default: false
    },

    // Pagination props
    initialPageSize: {
      type: Number,
      default: 50
    },
    pageSizeOptions: {
      type: Array,
      default: () => [10, 25, 50, 100, 200]
    },

    // Virtual scrolling props
    virtualScrollEnabled: {
      type: Boolean,
      default: false
    },
    virtualScrollThreshold: {
      type: Number,
      default: 1000
    },
    rowHeight: {
      type: Number,
      default: 60
    },
    columnWidth: {
      type: Number,
      default: 150
    },
    bufferSize: {
      type: Number,
      default: 10
    },

    // Scroll sensitivity props
    scrollSensitivity: {
      type: Number,
      default: 1,
      validator: value => value >= 0.1 && value <= 2.0
    },
    smoothScrollEnabled: {
      type: Boolean,
      default: true
    },
    wheelScrollMultiplier: {
      type: Number,
      default: 2,
      validator: value => value >= 0.1 && value <= 2.0
    },

    // Performance props
    enablePerformanceMonitoring: {
      type: Boolean,
      default: false
    },
    freezeData: {
      type: Boolean,
      default: false
    },

    // Loading props
    loadingMessage: {
      type: String,
      default: 'Loading data...'
    },

    // Request configuration
    requestHeaders: {
      type: Object,
      default: () => ({})
    },
    requestOptions: {
      type: Object,
      default: () => ({})
    },
    autoRetry: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      // Component state
      currentPage: 1,
      pageSize: this.initialPageSize,

      // Selection state
      internalSelectedItems: [],

      // Performance tracking
      renderTime: 0,
      memoryUsage: 0,
      lastRenderTime: 0,

      // Scroll synchronization
      isScrollSyncing: false,

      // Internal state
      isDevelopment: process.env.NODE_ENV === 'development'
    }
  },

  computed: {
    /**
     * Container CSS classes
     */
    containerClasses() {
      return [
        'vue2-datatable',
        `vue2-datatable--${this.theme}`,
        {
          'vue2-datatable--striped': this.striped,
          'vue2-datatable--bordered': this.bordered,
          'vue2-datatable--hover': this.hover,
          'vue2-datatable--loading': this.loading,
          'vue2-datatable--virtual': this.shouldUseVirtualScrolling,
          'vue2-datatable--selectable': this.selectable
        }
      ]
    },

    /**
     * Process columns with defaults
     */
    processedColumns() {
      let columns = [...this.columns]

      // Add selection column if needed
      if (this.selectable) {
        columns.unshift({
          key: 'select',
          label: 'Select',
          sortable: false,
          searchable: false,
          width: '60px',
          fixed: 'left'
        })
      }

      return columns.map((col, index) => ({
        key: col.key,
        label: col.label || this.formatFieldName(col.key),
        sortable: this.sortable && col.sortable !== false,
        searchable: this.searchable && col.searchable !== false,
        width: col.width || this.columnWidth,
        type: col.type || 'text',
        format: col.format || null,
        align: col.align || 'left',
        fixed: col.fixed || null,
        render: col.render || null,
        formatter: col.formatter || null,
        validator: col.validator || null,
        index: index
      }))
    },

    /**
     * Get total filtered items count
     */
    totalFilteredItems() {
      return this.filteredItems ? this.filteredItems.length : 0
    },

    /**
     * Get total pages
     */
    totalPages() {
      if (this.shouldUseVirtualScrolling) return 1
      return Math.ceil(this.totalFilteredItems / this.pageSize)
    },

    /**
     * Get paginated items
     */
    paginatedItems() {
      if (this.shouldUseVirtualScrolling) {
        return this.sortedItems
      }

      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.sortedItems.slice(start, end)
    },

    /**
     * Check if retry is possible
     */
    canRetry() {
      return this.error && this.retryCount < this.maxRetries
    },

    /**
     * Get current selected items (internal or external)
     */
    currentSelectedItems() {
      // Prioritize external selectedItems prop, fallback to internal
      if (this.selectedItems && this.selectedItems.length > 0) {
        return this.selectedItems
      }
      return this.internalSelectedItems
    },

    /**
     * Check if all filtered items are selected
     */
    isAllSelected() {
      if (!this.selectable || this.totalFilteredItems === 0) return false
      return this.currentSelectedItems.length === this.totalFilteredItems &&
             this.filteredItems.every(item => this.isItemSelected(item))
    },

    /**
     * Check if selection is indeterminate (some but not all selected)
     */
    isIndeterminate() {
      if (!this.selectable || this.currentSelectedItems.length === 0) return false
      return this.currentSelectedItems.length > 0 && !this.isAllSelected
    }
  },

  mounted() {
    this.initializeComponent()

    // Ensure scroll synchronization after component is fully mounted
    this.$nextTick(() => {
      this.synchronizeScroll()
      this.ensureHorizontalScrollVisibility()
    })
  },

  beforeDestroy() {
    this.cleanupComponent()
  },

  methods: {
    /**
     * Initialize component
     */
    initializeComponent() {
      // Initialize scroll sensitivity settings
      this.initializeScrollSettings()

      // Initialize horizontal scrolling
      this.initializeHorizontalScrolling()

      // Start performance monitoring
      if (this.enablePerformanceMonitoring) {
        this.startPerformanceMonitoring()
      }

      // Validate props
      if (!validateProps(this.$props)) {
        console.error('Vue2DataTable: Invalid props provided')
      }
    },

    /**
     * Initialize horizontal scrolling to ensure scrollbars are visible
     */
    initializeHorizontalScrolling() {
      this.$nextTick(() => {
        const container = this.$refs.tableContainer
        if (container) {
          // Force scrollbar visibility by ensuring content width
          this.ensureHorizontalScrollVisibility()

          // Add resize observer to handle dynamic content changes
          if (window.ResizeObserver) {
            this.resizeObserver = new ResizeObserver(() => {
              this.ensureHorizontalScrollVisibility()
            })
            this.resizeObserver.observe(container)
          }

          // Add scroll event listener to the container
          container.addEventListener('scroll', this.handleScroll, { passive: true })

          // Add scroll event listener to header wrapper for bidirectional sync
          const headerWrapper = this.$refs.headerWrapper
          if (headerWrapper) {
            headerWrapper.addEventListener('scroll', this.handleHeaderScroll, { passive: true })
          }

          // Force horizontal scrollbar to be visible
          this.forceHorizontalScrollbarVisibility()
        }
      })
    },

    /**
     * Force horizontal scrollbar to be visible
     */
    forceHorizontalScrollbarVisibility() {
      const container = this.$refs.tableContainer
      if (!container) return

      // Force the container to always show horizontal scrollbar
      container.style.overflowX = 'scroll'
      container.style.scrollbarGutter = 'stable'

      // Add a temporary invisible element to force horizontal scrolling
      const forceScrollElement = document.createElement('div')
      forceScrollElement.style.position = 'absolute'
      forceScrollElement.style.top = '0'
      forceScrollElement.style.left = '0'
      forceScrollElement.style.width = '2000px' // Force width beyond container
      forceScrollElement.style.height = '1px'
      forceScrollElement.style.visibility = 'hidden'
      forceScrollElement.style.pointerEvents = 'none'
      forceScrollElement.className = 'force-horizontal-scroll'

      // Remove any existing force scroll element
      const existing = container.querySelector('.force-horizontal-scroll')
      if (existing) {
        existing.remove()
      }

      // Add the force scroll element
      container.appendChild(forceScrollElement)

      // Force layout recalculation
      container.offsetWidth
    },

    /**
     * Ensure horizontal scroll visibility when content exceeds container
     */
    ensureHorizontalScrollVisibility() {
      const container = this.$refs.tableContainer
      if (!container) return

      const headerWrapper = this.$refs.headerWrapper
      const bodyWrapper = this.$refs.bodyWrapper

      if (headerWrapper && bodyWrapper) {
        const headerTable = headerWrapper.querySelector('.header-table') || headerWrapper.querySelector('table')
        const bodyTable = bodyWrapper.querySelector('.body-table') || bodyWrapper.querySelector('table')

        if (headerTable && bodyTable) {
          // Calculate total column width based on actual columns
          const totalWidth = this.calculateTotalColumnWidth()
          const containerWidth = container.clientWidth

          // Always force a minimum width that exceeds container to ensure horizontal scrolling
          const minWidth = Math.max(totalWidth, containerWidth + 500) // Increased to 500px to guarantee scrolling

          // Apply width to both tables
          headerTable.style.minWidth = `${minWidth}px`
          bodyTable.style.minWidth = `${minWidth}px`
          headerTable.style.width = `${minWidth}px`
          bodyTable.style.width = `${minWidth}px`

          // Force container styles for horizontal scrolling
          container.style.overflowX = 'scroll'
          container.style.overflowY = 'auto'

          // Ensure scrollbar is always visible by setting scrollbar-gutter
          container.style.scrollbarGutter = 'stable'

          // Force layout recalculation
          container.offsetHeight // Trigger reflow

          // Synchronize column widths between header and body
          this.synchronizeColumnWidths(headerTable, bodyTable)

          // Debug logging in development
          if (process.env.NODE_ENV === 'development') {
            console.log('Vue2DataTable: Horizontal scroll setup', {
              containerWidth,
              totalWidth,
              minWidth,
              scrollWidth: container.scrollWidth,
              clientWidth: container.clientWidth,
              hasHorizontalScroll: container.scrollWidth > container.clientWidth,
              headerWrapper: !!headerWrapper,
              bodyWrapper: !!bodyWrapper
            })
          }
        }
      }
    },

    /**
     * Synchronize column widths between header and body tables
     */
    synchronizeColumnWidths(headerTable, bodyTable) {
      const headerCells = headerTable.querySelectorAll('th')
      const bodyCells = bodyTable.querySelector('tr')?.querySelectorAll('td')

      if (headerCells && bodyCells && headerCells.length === bodyCells.length) {
        // Get the computed widths from the body cells (which are more flexible)
        const columnWidths = Array.from(bodyCells).map(cell => {
          const computedStyle = window.getComputedStyle(cell)
          return computedStyle.width
        })

        // Apply the same widths to header cells
        headerCells.forEach((headerCell, index) => {
          if (columnWidths[index]) {
            headerCell.style.width = columnWidths[index]
            headerCell.style.minWidth = columnWidths[index]
          }
        })
      }
    },

    /**
     * Calculate total width needed for all columns
     */
    calculateTotalColumnWidth() {
      let totalWidth = 0
      const columnCount = this.visibleColumns.length

      // Calculate width based on actual columns
      this.visibleColumns.forEach(column => {
        if (column.key === 'select') {
          totalWidth += 60 // Selection column width
        } else if (column.width) {
          // Parse width if it's a string with 'px'
          const width = typeof column.width === 'string'
            ? parseInt(column.width.replace('px', ''))
            : column.width
          totalWidth += width
        } else {
          totalWidth += 180 // Default column width (increased from 150)
        }
      })

      // Ensure minimum total width to guarantee horizontal scrolling
      const minTotalWidth = Math.max(totalWidth, columnCount * 180, 1500) // Increased minimum

      return minTotalWidth
    },

    /**
     * Initialize scroll sensitivity settings
     */
    initializeScrollSettings() {
      // Pass scroll sensitivity props to the mixin
      this.scrollSensitivity = this.scrollSensitivity
      this.smoothScrollEnabled = this.smoothScrollEnabled
      this.wheelScrollMultiplier = this.wheelScrollMultiplier

      // Initialize target scroll positions
      this.targetScrollTop = 0
      this.targetScrollLeft = 0
    },

    /**
     * Start performance monitoring
     */
    startPerformanceMonitoring() {
      this.$nextTick(() => {
        this.updatePerformanceMetrics()
        
        // Update metrics periodically
        this.performanceInterval = setInterval(() => {
          this.updatePerformanceMetrics()
        }, 1000)
      })
    },

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics() {
      this.renderTime = performance.now() - this.lastRenderTime
      this.memoryUsage = globalPerformanceMonitor.getMemoryUsage()
      
      this.$emit('performance-update', {
        renderTime: this.renderTime,
        memoryUsage: this.memoryUsage,
        itemCount: this.totalFilteredItems
      })
    },

    /**
     * Handle page change
     */
    handlePageChange(page) {
      this.currentPage = page
      this.$emit('page-change', page)
    },

    /**
     * Handle page size change
     */
    handlePageSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.$emit('page-size-change', size)
    },

    /**
     * Handle row click
     */
    handleRowClick(payload) {
      const { item, index, event, selected } = payload

      // If row click selection is enabled and table is selectable
      if (this.selectOnRowClick && this.selectable) {
        // Toggle selection on row click
        this.handleRowSelect({
          item: item,
          selected: !selected
        })
      }

      this.$emit('row-click', { item, index, event, selected })
    },

    /**
     * Handle row selection
     */
    handleRowSelect(payload) {
      // Handle both direct calls and event payload
      let item, selected
      if (payload && typeof payload === 'object' && payload.item !== undefined) {
        // Called from event handler with payload object
        item = payload.item
        selected = payload.selected
      } else {
        // Called directly with item and selected parameters
        item = arguments[0]
        selected = arguments[1]
      }

      if (selected) {
        // Add item to selection if not already selected
        if (!this.isItemSelected(item)) {
          if (this.multipleSelection) {
            // Add to existing selection - use Vue.set for reactivity
            this.$set(this.internalSelectedItems, this.internalSelectedItems.length, item)
          } else {
            // Replace selection with single item
            this.internalSelectedItems.splice(0, this.internalSelectedItems.length, item)
          }
        }
      } else {
        // Remove item from selection
        const filteredItems = this.internalSelectedItems.filter(selectedItem =>
          !this.compareItems(item, selectedItem)
        )
        this.internalSelectedItems.splice(0, this.internalSelectedItems.length, ...filteredItems)
      }

      this.$emit('row-select', { item, selected })
      this.$emit('selection-change', this.internalSelectedItems)
      this.$emit('update:selectedItems', this.internalSelectedItems)
    },

    /**
     * Handle row hover
     */
    handleRowHover(item, index, event) {
      this.$emit('row-hover', { item, index, event })
    },

    /**
     * Handle select all
     */
    handleSelectAll(selected) {
      if (selected) {
        // Select all filtered items - use splice for reactivity
        this.internalSelectedItems.splice(0, this.internalSelectedItems.length, ...this.filteredItems)
      } else {
        // Deselect all items - clear array reactively
        this.internalSelectedItems.splice(0, this.internalSelectedItems.length)
      }

      this.$emit('select-all', selected)
      this.$emit('selection-change', this.internalSelectedItems)
      this.$emit('update:selectedItems', this.internalSelectedItems)
    },

    /**
     * Handle column resize
     */
    handleColumnResize(column, width) {
      this.$emit('column-resize', { column, width })
    },

    /**
     * Synchronize header and body scroll positions - ENHANCED
     */
    synchronizeScroll() {
      if (!this.$refs.tableContainer) return

      const container = this.$refs.tableContainer
      const headerWrapper = this.$refs.headerWrapper
      const bodyWrapper = this.$refs.bodyWrapper

      if (headerWrapper && bodyWrapper) {
        // Get the current scroll position from the main container
        const scrollLeft = container.scrollLeft

        // Prevent infinite scroll loops
        if (this.isScrollSyncing) return
        this.isScrollSyncing = true

        try {
          // Apply scroll position to header wrapper to keep it in sync
          if (Math.abs(headerWrapper.scrollLeft - scrollLeft) > 1) {
            headerWrapper.scrollLeft = scrollLeft
          }

          // Also sync the body wrapper if it has its own scroll
          if (bodyWrapper.scrollLeft !== undefined && Math.abs(bodyWrapper.scrollLeft - scrollLeft) > 1) {
            bodyWrapper.scrollLeft = scrollLeft
          }

          // Find the actual table elements and sync their positions
          const headerTable = headerWrapper.querySelector('.header-table') || headerWrapper.querySelector('table')
          const bodyTable = bodyWrapper.querySelector('.body-table') || bodyWrapper.querySelector('table')

          if (headerTable && bodyTable) {
            // Ensure both tables have the same transform for perfect alignment
            const transform = `translateX(-${scrollLeft}px)`
            headerTable.style.transform = transform
            bodyTable.style.transform = transform
          }

          // Ensure header stays sticky during vertical scroll
          headerWrapper.style.position = 'sticky'
          headerWrapper.style.top = '0'
          headerWrapper.style.zIndex = '200'
          headerWrapper.style.backgroundColor = 'white'
        } finally {
          // Reset the sync flag after a short delay
          this.$nextTick(() => {
            this.isScrollSyncing = false
          })
        }
      }
    },

    /**
     * Handle scroll events on the main container
     */
    handleScroll(event) {
      const container = event.target
      this.scrollLeft = container.scrollLeft
      this.scrollTop = container.scrollTop

      // Synchronize header scroll
      this.synchronizeScroll()

      // Emit scroll event for parent components
      this.$emit('scroll', {
        scrollLeft: this.scrollLeft,
        scrollTop: this.scrollTop,
        scrollWidth: container.scrollWidth,
        scrollHeight: container.scrollHeight,
        clientWidth: container.clientWidth,
        clientHeight: container.clientHeight
      })
    },

    /**
     * Handle scroll events on the header wrapper
     */
    handleHeaderScroll(event) {
      if (this.isScrollSyncing) return

      const headerWrapper = event.target
      const container = this.$refs.tableContainer

      if (container && headerWrapper) {
        // Sync main container scroll position with header
        if (Math.abs(container.scrollLeft - headerWrapper.scrollLeft) > 1) {
          container.scrollLeft = headerWrapper.scrollLeft
          this.scrollLeft = headerWrapper.scrollLeft
        }
      }
    },

    /**
     * Handle wheel events for enhanced scrolling
     */
    handleWheel(event) {
      const container = this.$refs.tableContainer
      if (!container) return

      // Check if horizontal scrolling is needed
      if (container.scrollWidth > container.clientWidth) {
        // Allow horizontal scrolling with Shift + wheel
        if (event.shiftKey) {
          event.preventDefault()
          container.scrollLeft += event.deltaY * this.wheelScrollMultiplier
          return
        }
      }

      // Let the default vertical scrolling behavior continue
      // The scroll event will be handled by handleScroll method
    },

    /**
     * Retry data loading
     */
    retry() {
      this.resetError()
      this.loadData({ retry: true })
    },

    /**
     * Format field name
     */
    formatFieldName(fieldName) {
      return fieldName
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, str => str.toUpperCase())
        .trim()
    },

    /**
     * Check if item is selected
     */
    isItemSelected(item) {
      if (!this.selectable || this.currentSelectedItems.length === 0) {
        return false
      }

      return this.currentSelectedItems.some(selectedItem =>
        this.compareItems(item, selectedItem)
      )
    },

    /**
     * Compare two items for equality
     */
    compareItems(item1, item2) {
      // Try to use ID fields for comparison
      if (item1.id !== undefined && item2.id !== undefined) {
        return item1.id === item2.id
      }
      if (item1._id !== undefined && item2._id !== undefined) {
        return item1._id === item2._id
      }
      if (item1.uuid !== undefined && item2.uuid !== undefined) {
        return item1.uuid === item2.uuid
      }

      // Fallback to deep comparison for simple objects
      return JSON.stringify(item1) === JSON.stringify(item2)
    },

    /**
     * Get unique key for item
     */
    getItemKey(item, index) {
      // Try common ID fields
      if (item.id !== undefined) return item.id
      if (item._id !== undefined) return item._id
      if (item.uuid !== undefined) return item.uuid

      // Fallback to index
      return index
    },

    /**
     * Cleanup component - ENHANCED MEMORY MANAGEMENT
     */
    cleanupComponent() {
      try {
        // Clean up performance monitoring
        if (this.performanceInterval) {
          clearInterval(this.performanceInterval)
          this.performanceInterval = null
        }

        // Clean up resize observer
        if (this.resizeObserver) {
          this.resizeObserver.disconnect()
          this.resizeObserver = null
        }

        // Clean up scroll event listeners
        const container = this.$refs.tableContainer
        if (container) {
          container.removeEventListener('scroll', this.handleScroll)

          // Remove force scroll element
          const forceScrollElement = container.querySelector('.force-horizontal-scroll')
          if (forceScrollElement) {
            forceScrollElement.remove()
          }
        }

        // Clean up header scroll event listener
        const headerWrapper = this.$refs.headerWrapper
        if (headerWrapper) {
          headerWrapper.removeEventListener('scroll', this.handleHeaderScroll)
        }

        // Clean up debounced search function
        if (this.debouncedSearch) {
          this.debouncedSearch = null
        }

        // Clean up any remaining timeouts
        if (this.searchDebounceTimer) {
          clearTimeout(this.searchDebounceTimer)
          this.searchDebounceTimer = null
        }

        // Clean up scroll synchronization
        if (this._scrollSyncTimeout) {
          clearTimeout(this._scrollSyncTimeout)
          this._scrollSyncTimeout = null
        }

        // Clear internal data arrays
        this.internalSelectedItems.splice(0, this.internalSelectedItems.length)

        // Clean up global performance monitor
        if (globalPerformanceMonitor) {
          globalPerformanceMonitor.clear()
        }

        // Force cleanup of mixins
        if (this.cleanupVirtualScrolling) {
          this.cleanupVirtualScrolling()
        }
        if (this.cleanupDataManager) {
          this.cleanupDataManager()
        }

        // Memory cleanup hint
        if (window.gc && process.env.NODE_ENV === 'development') {
          setTimeout(() => window.gc(), 100)
        }
      } catch (error) {
        console.error('Vue2DataTable: Error during component cleanup:', error)
      }
    }
  },

  watch: {
    searchTerm: {
      handler(newVal) {
        this.internalSearchTerm = newVal
        if (this.debouncedSearch) {
          this.debouncedSearch(newVal)
        }
      },
      immediate: true
    },

    selectedItems: {
      handler(newVal) {
        // Sync external selectedItems prop with internal state
        if (newVal && newVal.length > 0) {
          this.internalSelectedItems.splice(0, this.internalSelectedItems.length, ...newVal)
        } else if (newVal && newVal.length === 0) {
          this.internalSelectedItems.splice(0, this.internalSelectedItems.length)
        }
      },
      immediate: true,
      deep: true
    },

    // ENHANCED: Watch scroll position for header synchronization
    scrollLeft: {
      handler() {
        this.$nextTick(() => {
          this.synchronizeScroll()
        })
      },
      immediate: true
    },

    // ENHANCED: Watch visible columns for layout updates
    visibleColumns: {
      handler() {
        this.$nextTick(() => {
          this.synchronizeScroll()
        })
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
/* Vue2 DataTable Styles - Matching GenericDataTable Design */

.vue2-datatable-container {
  position: relative;
  width: 100%;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* Performance Monitor */
.performance-monitor {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-family: monospace;
}

.performance-stats {
  display: flex;
  gap: 12px;
}

/* Enhanced Table Container with Scroll Sync */
.table-container-vue2 {
  position: relative;
  overflow: auto;
  max-height: 650px;
  height: 850px;
  border-radius: 16px;
  background: white;
  border: 1px solid #e2e8f0;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-font-smoothing: subpixel-antialiased;
  contain: content;
  min-height: 400px;
  margin-bottom: 0;
  /* Enhanced scrollbar visibility - always visible */
  scrollbar-width: auto;
  scrollbar-color: #cbd5e0 #f7fafc;
  /* Force scrollbars to always be visible */
  overflow-x: scroll !important;
  overflow-y: auto !important;
  /* Ensure content can extend beyond container */
  min-width: 100%;
  /* Force scrollbar gutter to reserve space */
  scrollbar-gutter: stable both-edges;
  /* Ensure horizontal scrolling is always available */
  scroll-behavior: smooth;
}

.table-container-vue2::-webkit-scrollbar {
  width: 16px;
  height: 16px;
  /* Always show scrollbars */
  -webkit-appearance: none;
  /* Force visibility */
  display: block !important;
}

.table-container-vue2::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 8px;
  /* Add border for better visibility */
  border: 1px solid #cbd5e0;
  /* Force track to be visible */
  opacity: 1 !important;
}

.table-container-vue2::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 8px;
  border: 2px solid #f1f5f9;
  /* Add minimum size to ensure visibility */
  min-height: 30px;
  min-width: 30px;
  /* Force thumb to be visible */
  opacity: 1 !important;
}

.table-container-vue2::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

.table-container-vue2::-webkit-scrollbar-thumb:active {
  background: #475569;
}

.table-container-vue2::-webkit-scrollbar-corner {
  background: #f1f5f9;
  border: 1px solid #cbd5e0;
}

/* Force horizontal scrollbar to always be visible */
.table-container-vue2::-webkit-scrollbar:horizontal {
  height: 16px !important;
  display: block !important;
}

.table-container-vue2::-webkit-scrollbar-track:horizontal {
  background: #f1f5f9 !important;
  border: 1px solid #cbd5e0 !important;
}

.table-container-vue2::-webkit-scrollbar-thumb:horizontal {
  background: #94a3b8 !important;
  min-width: 50px !important;
}

/* Enhanced Header and Body Wrappers */
.table-header-wrapper {
  position: sticky;
  top: 0;
  z-index: 200;
  background: white;
  border-bottom: 2px solid #e2e8f0;
  /* Allow horizontal scrolling but hide scrollbar */
  overflow-x: auto;
  overflow-y: hidden;
  /* Hide the scrollbar for header wrapper but keep functionality */
  scrollbar-width: none;
  -ms-overflow-style: none;
  /* Ensure the wrapper can scroll */
  width: 100%;

  &::-webkit-scrollbar {
    display: none;
  }
}

.table-body-wrapper {
  position: relative;
  overflow: visible;
  background: white;
  /* Ensure body wrapper doesn't interfere with scrolling */
  min-width: 100%;
}

/* Total Records Bar */
.total-records-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px 24px;
  border-radius: 16px 16px 0 0;
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.total-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.total-section,
.filter-section,
.page-section {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.total-icon,
.filter-icon,
.page-icon {
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.9);
}

.total-label,
.filter-text,
.page-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.total-count-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 16px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Error Container */
.error-container {
  padding: 20px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 16px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #dc2626;
  font-weight: 500;
}

.error-icon {
  width: 20px;
  height: 20px;
  color: #dc2626;
}

.retry-button {
  margin-left: auto;
}

/* Theme Variants */
.vue2-datatable--dark {
  background: #1a202c;
  color: white;

  .table-container-vue2 {
    background: #1a202c;
    border-color: #2d3748;
  }

  .total-records-bar {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }
}

.vue2-datatable--compact {
  .table-container-vue2 {
    height: 500px;
    max-height: 500px;
  }

  .total-records-bar {
    padding: 12px 20px;
  }
}

.vue2-datatable--minimal {
  box-shadow: none;
  border: 1px solid #e2e8f0;

  .table-container-vue2 {
    border: none;
    border-radius: 0;
  }

  .total-records-bar {
    background: #f8fafc;
    color: #374151;
    border-radius: 0;
  }
}

/* State Classes */
.vue2-datatable--loading {
  pointer-events: none;

  .table-container-vue2 {
    opacity: 0.7;
  }
}

.vue2-datatable--virtual {
  .table-container-vue2 {
    overflow: auto;
  }
}

/* Enhanced Mobile Scrolling */
@media (max-width: 768px) {
  .vue2-datatable-container {
    border-radius: 8px;
  }

  .table-container-vue2 {
    height: 600px;
    border-radius: 8px;
    /* Enhanced mobile scrollbar visibility */
    scrollbar-width: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-container-vue2::-webkit-scrollbar {
    width: 16px;
    height: 16px;
    /* Larger scrollbars for mobile */
  }

  .table-container-vue2::-webkit-scrollbar-thumb {
    background: #a0aec0;
    /* More prominent color for mobile */
    border: 1px solid #f7fafc;
  }

  .total-records-bar {
    padding: 12px 16px;
    border-radius: 8px 8px 0 0;
  }

  .total-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .performance-monitor {
    position: relative;
    top: 0;
    right: 0;
    margin-bottom: 16px;
  }

  .performance-stats {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 576px) {
  .table-container-vue2 {
    height: 500px;
  }

  .total-records-bar {
    padding: 10px 12px;
  }

  .total-section,
  .filter-section,
  .page-section {
    font-size: 12px;
  }

  .total-count-badge {
    padding: 4px 12px;
    font-size: 14px;
  }
}

/* Force horizontal scroll when content exceeds container */
.table-container-vue2 {
  /* Ensure horizontal scrolling is always available when needed */
  min-width: 0;
  width: 100%;
}

/* Ensure tables can expand horizontally and maintain column alignment */
.vue2-datatable-container {
  .table-header-container,
  .table-body-container {
    overflow-x: visible;
  }

  .header-table,
  .body-table {
    width: auto;
    /* Force minimum width to ensure horizontal scrolling */
    min-width: max(100%, 1500px) !important;
    /* Ensure both tables have the same layout */
    table-layout: fixed;
    /* Ensure smooth transitions for scroll synchronization */
    transition: transform 0.1s ease-out;
    /* Ensure tables are positioned for transform */
    position: relative;
  }

  /* Ensure column widths are consistent between header and body */
  .header-table th,
  .body-table td {
    box-sizing: border-box;
    /* Use wider minimum width to force horizontal scrolling */
    width: auto;
    min-width: 180px !important;
    /* Allow text wrapping for better content visibility */
    white-space: normal;
    overflow: visible;
    word-wrap: break-word;
    word-break: break-word;
    padding: 16px 20px;
    vertical-align: top;
    line-height: 1.5;
  }

  /* Specific width handling for selection column */
  .header-table th:first-child,
  .body-table td:first-child {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
  }

  /* Ensure proper table alignment */
  .table-header-wrapper,
  .table-body-wrapper {
    position: relative;
    /* Force content to extend beyond container width */
    min-width: 1500px;
  }

  /* Fix any potential layout issues */
  .header-table,
  .body-table {
    border-spacing: 0;
    border-collapse: collapse;
    /* Force table to be wider than container */
    width: 1500px !important;
    min-width: 1500px !important;
  }

  /* Ensure proper row height for wrapped content */
  .body-table tr {
    min-height: 60px;
  }

  .body-table td {
    min-height: 60px;
    height: auto;
  }
}


/* Animation and Transitions */
.vue2-datatable-container {
  transition: all 0.3s ease;
}

.table-container-vue2 {
  transition: opacity 0.2s ease;
}

.total-records-bar {
  transition: background 0.3s ease;
}

.error-container {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus and Accessibility */
.vue2-datatable-container:focus-within {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Print Styles */
@media print {
  .vue2-datatable-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .performance-monitor,
  .error-container {
    display: none;
  }

  .table-container-vue2 {
    height: auto;
    max-height: none;
    overflow: visible;
  }
}
</style>
